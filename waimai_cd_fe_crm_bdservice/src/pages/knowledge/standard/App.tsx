import { Button, Col, Form, Input, Row, Select, Table, Typography, App as AntdApp, Space } from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { ColumnsType } from 'antd/es/table';
import { ExportOutlined, PlusOutlined } from '@ant-design/icons';

import { AnswerType, Data } from '@src/pages/knowledge/types';
import useEditFaq from '@src/pages/knowledge/components/editor/editModal/editFaq';
import EditAndDelete from '@src/pages/knowledge/components/editAndDelete';
import { requestHandle } from '@src/pages/knowledge/consts';
import './style.scss';
import useUrlState from '@src/hooks/useUrlState';
import { useState } from 'react';
import usePermissions from '@src/hooks/usePermissions';
import openUrl from '@src/pages/knowledge/open';
import AnswerCol from '@src/pages/knowledge/components/AnswerCol';
import { DOMAIN, STANDARD_ANSWER, STANDARD_QUESTION } from '../text';
import DomainFormItem from '../components/DomainFormItem';
import { getDomainDesc, useDomainList } from '../hooks/domainList';
import { useStandardTable } from '../hooks/standardTable';

const getColumns: ({ deleteFn, editFn, domainList }) => ColumnsType<Data> = ({ deleteFn, editFn, domainList }) => [
    {
        dataIndex: 'id',
        title: 'ID',
    },
    {
        dataIndex: 'question',
        title: STANDARD_QUESTION,
        render: v => (
            <Typography.Text style={{ maxWidth: 200 }} ellipsis={{ tooltip: v }}>
                {v}
            </Typography.Text>
        ),
    },
    {
        dataIndex: 'domainId',
        title: DOMAIN,
        render: v => getDomainDesc(v, domainList),
    },
    {
        dataIndex: 'type',
        title: '答案类型',
        align: 'center',
        render: type => AnswerType[type],
    },
    {
        dataIndex: 'answer',
        title: STANDARD_ANSWER,
        width: 200,
        render: (answer, row) => <AnswerCol question={row.question} answer={answer} />,
    },
    {
        dataIndex: 'utime',
        title: '更新时间',
    },
    {
        dataIndex: 'modifierName',
        title: '更新人',
        render: (name, row) => [name, row.modifierMis].filter(v => v).join(' ') || '-',
    },
    // {
    //     dataIndex: 'id',
    //     title: '状态',
    // },
    {
        dataIndex: 'id',
        title: '操作',
        align: 'center',
        render: (id, row) => {
            return <EditAndDelete editFn={() => editFn(row)} deleteFn={() => deleteFn(row)} />;
        },
    },
];

const App = () => {
    const [form] = Form.useForm();
    const { modal } = AntdApp.useApp();

    const { search, run, pagination, params, tableProps } = useStandardTable(form);

    const { setUrlState } = useUrlState(urlState => {
        form.setFieldsValue(urlState);
    });

    const { submit } = search;
    const update = () => {
        run(pagination, params[1] as any);
    };
    const editOrNewFaq = useEditFaq(update);

    const deleteQuestion = ({ triggerId: id, type }) => {
        modal.confirm({
            title: '确定要删除该标准问吗？',
            content: '删除后无法恢复，是否确认删除？',
            onOk: async () => {
                const res = await apiCaller.get('/manage/phrase/standard/delete', { id, type });
                requestHandle(res, { successCallback: update });
            },
        });
    };
    const [exporting, setExporting] = useState(false);
    const { data: permissions } = usePermissions();
    const { data: domainList } = useDomainList();
    const formItemSpan = 4;
    return (
        <div>
            <Form
                form={form}
                colon={false}
                onValuesChange={() => {
                    setUrlState(form.getFieldsValue());
                    submit();
                }}
                onReset={() => {
                    setUrlState(form.getFieldsValue());
                    submit();
                }}
            >
                <Row gutter={20}>
                    <Col span={formItemSpan}>
                        <Form.Item name={'name'} label={STANDARD_QUESTION}>
                            <Input placeholder={'请输入关键词'} allowClear />
                        </Form.Item>
                    </Col>
                    <Col span={formItemSpan}>
                        <Form.Item name={'type'} label={'答案类型'}>
                            <Select
                                allowClear
                                placeholder={'请选择答案类型'}
                                options={[
                                    { label: '任务流', value: 'task' },
                                    { label: '问答类', value: 'faq' },
                                ]}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={formItemSpan}>
                        <DomainFormItem />
                    </Col>
                    <Col span={4}>
                        <Space>
                            <Button htmlType={'reset'}>重置</Button>
                            <Button type={'primary'} onClick={submit}>
                                查询
                            </Button>
                        </Space>
                    </Col>
                    <Col span={8}>
                        <Row justify={'end'}>
                            <Space>
                                <Button
                                    icon={<PlusOutlined />}
                                    onClick={() => {
                                        openUrl('/knowledge/batchImport', '_self');
                                    }}
                                >
                                    批量新增
                                </Button>
                                <Button onClick={() => editOrNewFaq()} icon={<PlusOutlined />}>
                                    新增
                                </Button>
                                {permissions?.includes('phrase_data_export') ? (
                                    <Button
                                        loading={exporting}
                                        onClick={async () => {
                                            setExporting(true);
                                            const res = await apiCaller.post('/manage/phrase/standard/export', {});
                                            setExporting(false);
                                            if (res.code !== 0) {
                                                return;
                                            }
                                            window.open(res.data);
                                        }}
                                        icon={<ExportOutlined />}
                                    >
                                        导出
                                    </Button>
                                ) : null}
                            </Space>
                        </Row>
                    </Col>
                </Row>

                {/*<Form.Item name={'status'} label={'状态'}>*/}
                {/*    <Select />*/}
                {/*</Form.Item>*/}
            </Form>
            <Table
                {...tableProps}
                columns={getColumns({
                    deleteFn: deleteQuestion,
                    editFn: (data: Data) => editOrNewFaq('edit', data) as any,
                    domainList,
                })}
                rowKey={'id'}
            />
        </div>
    );
};
export default App;
