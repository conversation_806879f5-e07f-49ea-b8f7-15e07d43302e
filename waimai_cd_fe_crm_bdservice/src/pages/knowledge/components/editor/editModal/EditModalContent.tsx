import { AI_PROPOSAL, STANDARD_ANSWER, STANDARD_QUESTION } from '@src/pages/knowledge/text';
import { Button, Col, ConfigProvider, Divider, Form, FormInstance, Input, Row, Space, Switch, Typography } from 'antd';
import DomainFormItem from '../../DomainFormItem';
import Quill2Editor from '../quill2';

const EditModalContent = ({
    form,
    data,
    config = {},
    mode,
    onOk,
    onCancel,
}: {
    form: FormInstance;
    data: any;
    config?: any;
    mode: any;
    onOk: () => void;
    onCancel: () => void;
}) => {
    const needTt = Form.useWatch('needTt', form);
    const type = data?.type || 'faq';
    return (
        <Form form={form} colon={false} disabled={data.type === 'task'} requiredMark={true}>
            <Row justify={'space-between'} align={'middle'}>
                <Typography.Text>{mode === 'new' ? `新增${AI_PROPOSAL}` : `编辑${AI_PROPOSAL}`}</Typography.Text>
                <Space>
                    <Button onClick={onCancel} disabled={false}>
                        取消
                    </Button>
                    <Button onClick={onOk} type={'primary'} disabled={false}>
                        确认
                    </Button>
                </Space>
            </Row>
            <Divider />
            <Row>
                <Col span={20}>
                    <Form.Item
                        required
                        name={'question'}
                        label={STANDARD_QUESTION}
                        rules={[{ required: true }]}
                        labelCol={{ span: 3 }}
                    >
                        <Input placeholder={'请输入标题'} disabled={config.question?.disabled ?? false} allowClear />
                    </Form.Item>
                </Col>
                {data.id ? (
                    <Col style={{ marginLeft: 8, color: '#222' }}>
                        {/*该Form.Item排版用*/}
                        <Form.Item>ID {data.id}</Form.Item>
                    </Col>
                ) : null}
            </Row>
            <Row>
                <Col span={20}>
                    <DomainFormItem
                        itemProps={{ label: '所属域', labelCol: { span: 3 } }}
                        selectProps={{ disabled: false }}
                    />
                </Col>
            </Row>
            <Row>
                <Col span={20}>
                    <Row>
                        <Col span={3} style={{ justifyContent: 'end', display: 'flex' }}>
                            <Form.Item label={STANDARD_ANSWER} required />
                        </Col>
                        <Col span={21}>
                            <Space>
                                <Form.Item name={'type'} style={{ marginBottom: 0 }}>
                                    <Switch disabled={true} checked={data.type === 'task'} />
                                </Form.Item>
                                <Form.Item label={'多轮任务流'} style={{ marginBottom: 0 }} />
                            </Space>

                            <Form.Item
                                name={'answer'}
                                rules={[type === 'faq' ? { required: true, message: '请输入答案' } : {}]}
                                style={{ marginBottom: 0 }}
                            >
                                <Quill2Editor style={{ maxWidth: 588 }} />
                            </Form.Item>
                            <ConfigProvider theme={{ components: { Card: { padding: 10 } } }}>
                                <div style={{ border: '1px solid #e2e2e2', borderRadius: 10, padding: 10 }}>
                                    <Row>
                                        <Col span={21}>
                                            <Form.Item
                                                style={{ marginBottom: 0 }}
                                                name={'ttUrl'}
                                                dependencies={['needTt']}
                                                rules={[{ required: needTt, message: '请输入tt链接' }]}
                                            >
                                                <Input
                                                    placeholder={'请输入tt链接'}
                                                    disabled={!form.getFieldValue('needTt')}
                                                    allowClear
                                                />
                                            </Form.Item>
                                        </Col>
                                        <Col span={3} style={{ justifyContent: 'center', display: 'flex' }}>
                                            <Form.Item
                                                name={'needTt'}
                                                valuePropName={'checked'}
                                                style={{ marginBottom: 0 }}
                                            >
                                                <Switch />
                                            </Form.Item>
                                        </Col>
                                    </Row>
                                </div>
                            </ConfigProvider>
                        </Col>
                    </Row>
                </Col>
            </Row>
        </Form>
    );
};
export default EditModalContent;
