import { App, Form } from 'antd';
import { Data } from '@src/pages/knowledge/types';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { requestHandle } from '@src/pages/knowledge/consts';
import _ from 'lodash';
import EditModalContent from './EditModalContent';

interface Standard {
    id: number;
    type: string;
    question: string;
    answer: string;
    ttUrl: string;
}

const useEditFaq = (update = () => {}) => {
    const { modal, message } = App.useApp();
    const [form] = Form.useForm();

    return async (mode: 'edit' | 'new' | 'batchImport' = 'new', data?: Data) => {
        const id = data?.triggerId;
        const openModal = (data: Partial<Standard>) => {
            form.resetFields();
            form.setFieldsValue(data);

            // 编辑模式下如果没有ttUrl则回填notNeedTt为true
            if (data.id && !data.ttUrl) {
                form.setFieldValue('notNeedTt', true);
            }

            const modalIns = modal.confirm({
                icon: null,
                // title: mode === 'new' ? `新增${AI_PROPOSAL}` : `编辑${AI_PROPOSAL}`,
                title: null,
                width: 866,
                footer: null,
                content: (
                    <EditModalContent
                        onCancel={() => modalIns.destroy()}
                        onOk={async () => {
                            const values = await form.validateFields();
                            const successCallback = () => {
                                update();
                                modalIns.destroy();
                            };
                            // 批量导入模式
                            if (mode === 'batchImport') {
                                const res = await apiCaller.post('/manage/phrase/standard/batch/edit', {
                                    ...values,
                                    id: data.id,
                                });
                                requestHandle(res, { successCallback });
                                return;
                            }
                            // 普通标准问
                            if (id) {
                                const res = await apiCaller.post('/manage/phrase/standard/modify', { ...values, id });
                                requestHandle(res, { successCallback });
                                return;
                            }
                            // 多轮类型修改
                            const res = await apiCaller.post('/manage/phrase/standard/add', { ...values, type: 'faq' });
                            requestHandle(res, { successCallback });
                        }}
                        data={data}
                        form={form}
                        mode={mode}
                        config={mode === 'batchImport' ? { question: { disabled: true } } : {}}
                    />
                ),
            });
            return modalIns;
        };
        // 任务流类型只支持修改问题名称, 因此不需要请求详情
        // 批量导入模式不需要重新查询
        if (data?.type === 'task' || mode === 'batchImport') {
            return openModal(data as any);
        }
        // 问答类详情数据和list获取的不一致，需要进行重新请求
        if (id) {
            const res = await apiCaller.get('/manage/phrase/standard/detail', {
                ..._.pick(data, ['type']),
                id: String(id),
            });
            return requestHandle(res, {
                silent: true,
                successCallback: res => openModal(res.data as Standard),
            });
        }
        return openModal({});
    };
};
export default useEditFaq;
