import { Col, Image, Row } from 'antd';
import openLink from '@src/pages/knowledge/chat/common/utils/openLink';

const MediaRender = ({ item }) => {
    return (
        <Row gutter={10} key={item.localId}>
            {item.insert.media.map((v: any) => {
                if (!v) {
                    return null;
                }
                if (v?.image) {
                    return (
                        <Col key={v.image} span={5}>
                            <Image src={v.image} />
                        </Col>
                    );
                }
                return (
                    <Col key={v.video} span={5}>
                        <video src={v.video} width={'100%'} onClick={() => openLink(v.video)} />
                    </Col>
                );
            })}
        </Row>
    );
};
export default MediaRender;
